using Mirror;
using Steamworks;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MyNetworkManager : NetworkManager
{
    public static bool isMultiplayer;
    public static MyNetworkManager instance; // Static instance

    public override void Awake()
    {
        base.Awake();
        if (instance == null) 
        {
            instance = this;
        }
        else 
        {
            Destroy(gameObject); 
            return;
        }
        EnsureLobbyPlayerListExists();
    }

    public override void OnStartServer()
    {
        base.OnStartServer();
        EnsureLobbyPlayerListExists();
    }

    private void EnsureLobbyPlayerListExists()
    {
        if (LobbyPlayerList.instance == null)
        {
            LobbyPlayerList listComponent = GetComponent<LobbyPlayerList>();
            if (listComponent == null)
            {
                Debug.LogWarning("MyNetworkManager: LobbyPlayerList component not found on NetworkManager GameObject. Please add it in the Editor, and ensure a NetworkIdentity is present.");
            }
            
            if (LobbyPlayerList.instance == null && listComponent != null)
            {
                Debug.Log("MyNetworkManager: LobbyPlayerList.instance was set by finding component.");
            }
            else if (LobbyPlayerList.instance != null)
            {
                Debug.Log("MyNetworkManager: LobbyPlayerList.instance is available.");
            }
            else
            {
                Debug.LogError("MyNetworkManager: Failed to find LobbyPlayerList.instance. Critical error.");
            }
        }
    }

    public override void OnServerAddPlayer(NetworkConnectionToClient conn)
    {
        base.OnServerAddPlayer(conn);
        MyClient client = conn.identity.GetComponent<MyClient>();
        InitializePlayerClient(conn, client);
        
        if (LobbyPlayerList.instance != null)
        {
            LobbyPlayerList.instance.allClients.Add(client);
        }
        else
        {
            Debug.LogError("LobbyPlayerList.instance is null. Cannot add client to list.");
        }
    }

    private void InitializePlayerClient(NetworkConnectionToClient conn, MyClient client)
    {
        CSteamID steamId = GetSteamIDForConnection(conn);
        SetPlayerInfoAndAvatar(client, steamId, conn);
    }

    private CSteamID GetSteamIDForConnection(NetworkConnectionToClient conn)
    {
        if (conn == NetworkServer.localConnection)
        {
            return SteamUser.GetSteamID();
        }

        if (conn.authenticationData is CSteamID authSteamId && authSteamId.IsValid())
        {
            return authSteamId;
        }

        if (conn.authenticationData is ulong authUlongSteamIdValue && authUlongSteamIdValue != 0)
        {
            return new CSteamID(authUlongSteamIdValue);
        }
        
        
        if (SteamLobby.LobbyID.IsValid() && SteamLobby.LobbyID.m_SteamID != 0)
        {
            
            int numLobbyMembers = SteamMatchmaking.GetNumLobbyMembers(SteamLobby.LobbyID);
            
        }
        
        return CSteamID.Nil;
    }

    private void SetPlayerInfoAndAvatar(MyClient client, CSteamID steamId, NetworkConnectionToClient conn)
    {
        if (steamId.IsValid() && steamId != CSteamID.Nil)
        {
            string personaName = SteamFriends.GetFriendPersonaName(steamId);
            client.playerInfo = new PlayerInfoData(personaName, steamId.m_SteamID);

            Texture2D avatarTexture = SteamHelper.GetAvatar(steamId);
            if (avatarTexture != null)
            {
                client.avatarData = avatarTexture.EncodeToPNG();
                Destroy(avatarTexture); 
            }
            else
            {
                client.avatarData = null;
            }
        }
        else
        {
            string playerName = (conn == NetworkServer.localConnection) ? "Host" : ("Player " + conn.connectionId.ToString());
            client.playerInfo = new PlayerInfoData(playerName, 0);
            client.avatarData = null;
        }
    }

    public override void OnServerDisconnect(NetworkConnectionToClient conn)
    {
        if (conn.identity != null)
        {
            MyClient client = conn.identity.GetComponent<MyClient>();
            if (client != null && LobbyPlayerList.instance != null)
            {
                LobbyPlayerList.instance.allClients.Remove(client);
            }
            else if (LobbyPlayerList.instance == null)
            {
                 Debug.LogError("LobbyPlayerList.instance is null during OnServerDisconnect. Cannot remove client from list.");
            }
        }
        base.OnServerDisconnect(conn);
    }

    public override void OnStartClient()
    {
        if (isMultiplayer)
        {
            MainMenu.instance.SetMenuState(MenuState.InParty);
            PopupManager.instance.Popup_Close();
        }

        base.OnStartClient();
    }

    public override void OnStopClient()
    {
        if (isMultiplayer)
        {
            if (LobbyPlayerList.instance != null && NetworkClient.active) // Check if client is active before clearing
            {
                Debug.Log("MyNetworkManager: OnStopClient - Clearing LobbyPlayerList.instance");
                LobbyPlayerList.instance.allClients.Clear();
            }
            MainMenu.instance.SetMenuState(MenuState.Home);
        }

        base.OnStopClient();
    }

    public override void OnStopServer()
    {
        if (LobbyPlayerList.instance != null)
        {
            LobbyPlayerList.instance.allClients.Clear();
        }
        base.OnStopServer();
    }

    public void SetMultiplayer(bool value)
    {
        isMultiplayer = value;

        if (isMultiplayer)
            NetworkServer.dontListen = false;
        else
            NetworkServer.dontListen = true;
    }
}
